import Link from 'next/link';

export default function HeroSection() {
  return (
    <section className="relative h-screen flex items-center justify-center -mt-20">
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('/hero-bg.jpg')`
        }}
      />
      
      {/* Content */}
      <div className="relative z-10 text-center text-white px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
        {/* Tagline */}
        <p className="text-lg sm:text-xl font-medium mb-6 tracking-wide">
          Empowering Minds
        </p>
        
        {/* Main Headline */}
        <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-bold mb-6 tracking-wider">
          <span className="block">SHAPING</span>
          <span className="block">FUTURES</span>
        </h1>
        
        {/* Subtitle */}
        <p className="text-xl sm:text-2xl font-light mb-8 tracking-wide">
          Your Partner in Academic Excellence
        </p>
        
        {/* CTA Button */}
        <Link 
          href="/contact"
          className="inline-block bg-white text-gray-900 px-8 py-3 rounded-md font-semibold text-lg hover:bg-gray-100 transition-colors duration-300 shadow-lg"
        >
          Contact Us
        </Link>
      </div>
    </section>
  );
}

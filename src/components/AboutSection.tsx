import Image from 'next/image';

export default function AboutSection() {
  return (
    <section id="about" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Side - Content */}
          <div className="space-y-8">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 tracking-wide">
                ABOUT US
              </h2>
              <div className="w-16 h-1 bg-gray-900 mb-8"></div>
            </div>
            
            <div className="space-y-6">
              <p className="text-gray-700 text-lg leading-relaxed">
                At Bright Path Educational Consulting, we believe every student has the potential to succeed. 
                Our mission is to provide personalized educational support that empowers students to reach 
                their academic goals and unlock their full potential.
              </p>
              
              <p className="text-gray-700 text-lg leading-relaxed">
                With decades of combined experience in education, special needs advocacy, and academic 
                counseling, our team is dedicated to creating individualized learning solutions that 
                address each student's unique needs and learning style.
              </p>
              
              <p className="text-gray-700 text-lg leading-relaxed">
                Whether you're looking for tutoring support, special education advocacy, or guidance 
                through the admissions process, we're here to light the path to your educational success.
              </p>
            </div>

            <div className="pt-4">
              <div className="grid grid-cols-2 gap-8">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">15+</h3>
                  <p className="text-gray-600">Years of Experience</p>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">500+</h3>
                  <p className="text-gray-600">Students Helped</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Image */}
          <div className="relative">
            <div className="relative w-full h-96 lg:h-[500px]">
              <Image
                src="/about-image.jpg"
                alt="Educational consulting and tutoring session"
                fill
                className="object-cover rounded-lg shadow-lg"
                priority
              />
            </div>
            
            {/* Decorative elements */}
            <div className="absolute -top-4 -left-4 w-24 h-24 bg-blue-100 rounded-full opacity-50 -z-10"></div>
            <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-green-100 rounded-full opacity-50 -z-10"></div>
          </div>
        </div>
      </div>
    </section>
  );
}

'use client';

import { useState } from 'react';

export default function ContactSection() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    // You can add form submission logic here
  };

  return (
    <section id="contact" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Left Side - Contact Information */}
          <div className="space-y-8">
            {/* Contact Heading */}
            <div>
              <div className="w-16 h-1 bg-black mb-6"></div>
              <h2 className="text-4xl font-bold text-black tracking-wide mb-8">
                CONTACT
              </h2>
            </div>

            {/* Contact Details */}
            <div className="space-y-6">
              <div>
                <p className="text-gray-700 text-lg">
                  DMV area in Maryland
                </p>
              </div>

              <div>
                <p className="text-gray-700 text-lg">
                  Tel: (301)518-5305
                </p>
              </div>

              <div>
                <p className="text-gray-700 text-lg">
                  <EMAIL>
                </p>
              </div>
            </div>

            {/* Footer */}
            <div className="pt-16">
              <p className="text-gray-500 text-sm">
                © 2023 by Bright Path Educational Consulting. Powered and secured by{' '}
                <a 
                  href="https://www.wix.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="underline hover:text-gray-700 transition-colors"
                >
                  Wix
                </a>
              </p>
            </div>
          </div>

          {/* Right Side - Contact Form */}
          <div className="space-y-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Name Field */}
              <div>
                <label htmlFor="name" className="block text-gray-700 text-sm mb-2">
                  Enter Your Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border-2 border-gray-300 focus:border-gray-500 focus:outline-none transition-colors"
                  required
                />
              </div>

              {/* Email Field */}
              <div>
                <label htmlFor="email" className="block text-gray-700 text-sm mb-2">
                  Enter Your Email *
                </label>
                <div className="relative">
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border-2 border-gray-300 focus:border-gray-500 focus:outline-none transition-colors pr-12"
                    required
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="w-6 h-6 bg-red-500 rounded flex items-center justify-center">
                      <span className="text-white text-xs font-bold">@</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Subject Field */}
              <div>
                <label htmlFor="subject" className="block text-gray-700 text-sm mb-2">
                  Enter Your Subject
                </label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border-2 border-gray-300 focus:border-gray-500 focus:outline-none transition-colors"
                />
              </div>

              {/* Message Field */}
              <div>
                <label htmlFor="message" className="block text-gray-700 text-sm mb-2">
                  Message
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  rows={6}
                  className="w-full px-4 py-3 border-2 border-gray-300 focus:border-gray-500 focus:outline-none transition-colors resize-vertical"
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <button
                  type="submit"
                  className="bg-black text-white px-8 py-3 hover:bg-gray-800 transition-colors duration-300 font-medium"
                >
                  Submit
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
}

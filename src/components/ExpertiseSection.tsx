import Image from 'next/image';

export default function ExpertiseSection() {
  return (
    <section id="expertise" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <p className="text-sm font-medium text-gray-600 mb-2 tracking-wide">
            EDUCATIONAL
          </p>
          <p className="text-sm font-medium text-gray-600 mb-8 tracking-wide">
            CONSULTING
          </p>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 tracking-wider">
            OUR EXPERTISE
          </h2>
          <p className="text-gray-700 text-lg max-w-3xl mx-auto">
            Our expert tutors have a wide range of expertise to help your child succeed in all academic disciplines.
          </p>
        </div>

        {/* Expertise Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12 lg:gap-16">
          {/* STEM */}
          <div className="text-center">
            <div className="relative w-48 h-48 mx-auto mb-8">
              <Image
                src="/stem-image.jpg"
                alt="STEM education with students working on science projects"
                fill
                className="rounded-full object-cover"
                priority
              />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              STEM
            </h3>
            <ul className="text-left space-y-3 max-w-xs mx-auto">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span className="text-gray-700">Computer Science</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span className="text-gray-700">Biology</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span className="text-gray-700">Math</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span className="text-gray-700">Engineering</span>
              </li>
            </ul>
          </div>

          {/* Humanities */}
          <div className="text-center">
            <div className="relative w-48 h-48 mx-auto mb-8">
              <Image
                src="/humanities-image.jpg"
                alt="Humanities education with books and learning materials"
                fill
                className="rounded-full object-cover"
                priority
              />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              Humanities
            </h3>
            <ul className="text-left space-y-3 max-w-xs mx-auto">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span className="text-gray-700">History</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span className="text-gray-700">English</span>
              </li>
            </ul>
          </div>

          {/* Special Education */}
          <div className="text-center">
            <div className="relative w-48 h-48 mx-auto mb-8">
              <Image
                src="/special-education-image.jpg"
                alt="Special education support with individualized learning"
                fill
                className="rounded-full object-cover"
                priority
              />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              Special Education
            </h3>
            <ul className="text-left space-y-3 max-w-xs mx-auto">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span className="text-gray-700">Orton Gillingham Reading Specialists</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span className="text-gray-700">Neurodivergent students</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span className="text-gray-700">Special needs students</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}

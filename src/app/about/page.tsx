import Header from '@/components/Header';
import Image from 'next/image';

export default function About() {
  return (
    <div className="min-h-screen">
      <Header />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Section Header */}
        <div className="mb-16">
          <div className="flex items-center mb-8">
            <div className="w-16 h-0.5 bg-gray-400 mr-6"></div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 tracking-wide">ABOUT US</h1>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-start">
          {/* Left Column - Text Content */}
          <div className="space-y-6">
            <p className="text-gray-700 leading-relaxed">
              At Bright Path Educational Consulting, we believe that every child has the potential to succeed when provided with the right guidance and support. Our mission is to ensure that all children have access to the resources they need to thrive in every area of academia. We work closely with parents to tailor our services to the unique needs of each child, offering tutoring, admissions counseling, and special education advocacy to help them achieve their academic goals.
            </p>

            <p className="text-gray-700 leading-relaxed">
              Founded in 2021, Bright Path was born out of a desire to extend our role beyond the classroom. We engage with children in more comfortable, personalized settings, providing them with the tools and confidence they need to excel. Our dedicated team of professional educators brings over six decades of experience in delivering exemplary educational services to students. Under the leadership of our founder, a former director of special education services, we are committed to making a lasting impact on the lives of the children we serve.
            </p>

            <p className="text-gray-700 leading-relaxed">
              Based in the DMV area, we are proud to support families in our community, guiding each child along their unique educational journey.
            </p>
          </div>

          {/* Right Column - Image */}
          <div className="relative">
            <Image
              src="/about-us-image.jpg"
              alt="Educational consulting session with students and instructor"
              width={600}
              height={400}
              className="rounded-lg shadow-lg object-cover w-full h-auto"
              priority
            />
          </div>
        </div>
      </main>
    </div>
  );
}

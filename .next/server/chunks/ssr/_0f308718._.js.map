{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/HeroSection.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport default function HeroSection() {\n  return (\n    <section className=\"relative h-screen flex items-center justify-center -mt-20\">\n      {/* Background Image */}\n      <div \n        className=\"absolute inset-0 bg-cover bg-center bg-no-repeat\"\n        style={{\n          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('/hero-bg.jpg')`\n        }}\n      />\n      \n      {/* Content */}\n      <div className=\"relative z-10 text-center text-white px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto\">\n        {/* Tagline */}\n        <p className=\"text-lg sm:text-xl font-medium mb-6 tracking-wide\">\n          Empowering Minds\n        </p>\n        \n        {/* Main Headline */}\n        <h1 className=\"text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-bold mb-6 tracking-wider\">\n          <span className=\"block\">SHAPING</span>\n          <span className=\"block\">FUTURES</span>\n        </h1>\n        \n        {/* Subtitle */}\n        <p className=\"text-xl sm:text-2xl font-light mb-8 tracking-wide\">\n          Your Partner in Academic Excellence\n        </p>\n        \n        {/* CTA Button */}\n        <Link \n          href=\"/contact\"\n          className=\"inline-block bg-white text-gray-900 px-8 py-3 rounded-md font-semibold text-lg hover:bg-gray-100 transition-colors duration-300 shadow-lg\"\n        >\n          Contact Us\n        </Link>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC,4EAA4E,CAAC;gBACjG;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAE,WAAU;kCAAoD;;;;;;kCAKjE,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAK,WAAU;0CAAQ;;;;;;0CACxB,8OAAC;gCAAK,WAAU;0CAAQ;;;;;;;;;;;;kCAI1B,8OAAC;wBAAE,WAAU;kCAAoD;;;;;;kCAKjE,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/AboutSection.tsx"], "sourcesContent": ["import Image from 'next/image';\n\nexport default function AboutSection() {\n  return (\n    <section id=\"about\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n          {/* Left Side - Content */}\n          <div className=\"space-y-8\">\n            <div>\n              <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6 tracking-wide\">\n                ABOUT US\n              </h2>\n              <div className=\"w-16 h-1 bg-gray-900 mb-8\"></div>\n            </div>\n            \n            <div className=\"space-y-6\">\n              <p className=\"text-gray-700 text-lg leading-relaxed\">\n                At Bright Path Educational Consulting, we believe every student has the potential to succeed. \n                Our mission is to provide personalized educational support that empowers students to reach \n                their academic goals and unlock their full potential.\n              </p>\n              \n              <p className=\"text-gray-700 text-lg leading-relaxed\">\n                With decades of combined experience in education, special needs advocacy, and academic \n                counseling, our team is dedicated to creating individualized learning solutions that \n                address each student's unique needs and learning style.\n              </p>\n              \n              <p className=\"text-gray-700 text-lg leading-relaxed\">\n                Whether you're looking for tutoring support, special education advocacy, or guidance \n                through the admissions process, we're here to light the path to your educational success.\n              </p>\n            </div>\n\n            <div className=\"pt-4\">\n              <div className=\"grid grid-cols-2 gap-8\">\n                <div>\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">15+</h3>\n                  <p className=\"text-gray-600\">Years of Experience</p>\n                </div>\n                <div>\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">500+</h3>\n                  <p className=\"text-gray-600\">Students Helped</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Side - Image */}\n          <div className=\"relative\">\n            <div className=\"relative w-full h-96 lg:h-[500px]\">\n              <Image\n                src=\"/about-image.jpg\"\n                alt=\"Educational consulting and tutoring session\"\n                fill\n                className=\"object-cover rounded-lg shadow-lg\"\n                priority\n              />\n            </div>\n            \n            {/* Decorative elements */}\n            <div className=\"absolute -top-4 -left-4 w-24 h-24 bg-blue-100 rounded-full opacity-50 -z-10\"></div>\n            <div className=\"absolute -bottom-4 -right-4 w-32 h-32 bg-green-100 rounded-full opacity-50 -z-10\"></div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAkE;;;;;;kDAGhF,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAMrD,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAMrD,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;0CAMvD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAE/B,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,WAAU;oCACV,QAAQ;;;;;;;;;;;0CAKZ,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/ServicesSection.tsx"], "sourcesContent": ["export default function ServicesSection() {\n  return (\n    <section\n      id=\"services\"\n      className=\"relative py-20 bg-cover bg-center bg-no-repeat\"\n      style={{\n        backgroundImage: \"linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('/services-bg.jpg')\"\n      }}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-4 tracking-wide\">\n            HOW WE CAN\n          </h2>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white tracking-wide\">\n            HELP YOU\n          </h2>\n        </div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12\">\n          {/* Tutoring Services */}\n          <div className=\"bg-black bg-opacity-40 backdrop-blur-sm border border-white border-opacity-30 rounded-lg p-8 text-white\">\n            <h3 className=\"text-2xl font-bold mb-6 text-center text-white\">\n              Tutoring Services\n            </h3>\n            <p className=\"text-white leading-relaxed mb-6\">\n              We tutor a variety of subjects such as, Biology, Math, Computer Science, and English. We Tutor kids of all ages from Elementary to High School. Tutoring Sessions last one hour. Call or Book now!\n            </p>\n          </div>\n\n          {/* Special Education Advocacy */}\n          <div className=\"bg-black bg-opacity-40 backdrop-blur-sm border border-white border-opacity-30 rounded-lg p-8 text-white\">\n            <h3 className=\"text-2xl font-bold mb-6 text-center text-white\">\n              Special Education Advocacy\n            </h3>\n            <p className=\"text-white leading-relaxed mb-6\">\n              Our special education advocates have decades of experience ensuring that your child gets the services that they need. Call for more information.\n            </p>\n          </div>\n\n          {/* Admissions Assistance */}\n          <div className=\"bg-black bg-opacity-40 backdrop-blur-sm border border-white border-opacity-30 rounded-lg p-8 text-white\">\n            <h3 className=\"text-2xl font-bold mb-6 text-center text-white\">\n              Admissions Assistance\n            </h3>\n            <p className=\"text-white leading-relaxed mb-6\">\n              Navigating the next chapter can be complex. Let us help you set your child up for a successful future. Call for more information.\n            </p>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QACC,IAAG;QACH,WAAU;QACV,OAAO;YACL,iBAAiB;QACnB;kBAEA,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA+D;;;;;;sCAG7E,8OAAC;4BAAG,WAAU;sCAA0D;;;;;;;;;;;;8BAM1E,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG/D,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAMjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG/D,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAMjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG/D,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D", "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/ExpertiseSection.tsx"], "sourcesContent": ["import Image from 'next/image';\n\nexport default function ExpertiseSection() {\n  return (\n    <section id=\"expertise\" className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6 tracking-wider\">\n            OUR EXPERTISE\n          </h2>\n          <p className=\"text-gray-700 text-lg max-w-3xl mx-auto\">\n            Our expert tutors have a wide range of expertise to help your child succeed in all academic disciplines.\n          </p>\n        </div>\n\n        {/* Expertise Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-12 lg:gap-16\">\n          {/* STEM */}\n          <div className=\"text-center\">\n            <div className=\"relative w-48 h-48 mx-auto mb-8\">\n              <Image\n                src=\"/stem-image.jpg\"\n                alt=\"STEM education with students working on science projects\"\n                fill\n                className=\"rounded-full object-cover\"\n                priority\n              />\n            </div>\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">\n              STEM\n            </h3>\n            <ul className=\"text-left space-y-3 max-w-xs mx-auto\">\n              <li className=\"flex items-start\">\n                <span className=\"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0\"></span>\n                <span className=\"text-gray-700\">Computer Science</span>\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0\"></span>\n                <span className=\"text-gray-700\">Biology</span>\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0\"></span>\n                <span className=\"text-gray-700\">Math</span>\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0\"></span>\n                <span className=\"text-gray-700\">Engineering</span>\n              </li>\n            </ul>\n          </div>\n\n          {/* Humanities */}\n          <div className=\"text-center\">\n            <div className=\"relative w-48 h-48 mx-auto mb-8\">\n              <Image\n                src=\"/humanities-image.jpg\"\n                alt=\"Humanities education with books and learning materials\"\n                fill\n                className=\"rounded-full object-cover\"\n                priority\n              />\n            </div>\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">\n              Humanities\n            </h3>\n            <ul className=\"text-left space-y-3 max-w-xs mx-auto\">\n              <li className=\"flex items-start\">\n                <span className=\"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0\"></span>\n                <span className=\"text-gray-700\">History</span>\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0\"></span>\n                <span className=\"text-gray-700\">English</span>\n              </li>\n            </ul>\n          </div>\n\n          {/* Special Education */}\n          <div className=\"text-center\">\n            <div className=\"relative w-48 h-48 mx-auto mb-8\">\n              <Image\n                src=\"/special-education-image.jpg\"\n                alt=\"Special education support with individualized learning\"\n                fill\n                className=\"rounded-full object-cover\"\n                priority\n              />\n            </div>\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">\n              Special Education\n            </h3>\n            <ul className=\"text-left space-y-3 max-w-xs mx-auto\">\n              <li className=\"flex items-start\">\n                <span className=\"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0\"></span>\n                <span className=\"text-gray-700\">Orton Gillingham Reading Specialists</span>\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0\"></span>\n                <span className=\"text-gray-700\">Neurodivergent students</span>\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0\"></span>\n                <span className=\"text-gray-700\">Special needs students</span>\n              </li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,IAAG;QAAY,WAAU;kBAChC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmE;;;;;;sCAGjF,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,QAAQ;;;;;;;;;;;8CAGZ,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;sCAMtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,QAAQ;;;;;;;;;;;8CAGZ,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;sCAMtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,QAAQ;;;;;;;;;;;8CAGZ,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 920, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/ContactSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ContactSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ContactSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "debugId": null}}, {"offset": {"line": 934, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/ContactSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ContactSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ContactSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/ChatWidget.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ChatWidget.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ChatWidget.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/ChatWidget.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ChatWidget.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ChatWidget.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 986, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 996, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/app/page.tsx"], "sourcesContent": ["import Header from '@/components/Header';\nimport HeroSection from '@/components/HeroSection';\nimport AboutSection from '@/components/AboutSection';\nimport ServicesSection from '@/components/ServicesSection';\nimport ExpertiseSection from '@/components/ExpertiseSection';\nimport ContactSection from '@/components/ContactSection';\nimport ChatWidget from '@/components/ChatWidget';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <Header />\n      <main className=\"pt-20\">\n        <HeroSection />\n        <AboutSection />\n        <ServicesSection />\n        <ExpertiseSection />\n        <ContactSection />\n      </main>\n      <ChatWidget />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,iIAAA,CAAA,UAAW;;;;;kCACZ,8OAAC,kIAAA,CAAA,UAAY;;;;;kCACb,8OAAC,qIAAA,CAAA,UAAe;;;;;kCAChB,8OAAC,sIAAA,CAAA,UAAgB;;;;;kCACjB,8OAAC,oIAAA,CAAA,UAAc;;;;;;;;;;;0BAEjB,8OAAC,gIAAA,CAAA,UAAU;;;;;;;;;;;AAGjB", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\n\nconst scrollToSection = (sectionId: string) => {\n  const element = document.getElementById(sectionId);\n  if (element) {\n    element.scrollIntoView({ behavior: 'smooth' });\n  }\n};\n\nexport default function Header() {\n  return (\n    <header className=\"bg-white shadow-sm fixed w-full top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-6\">\n          {/* Logo/Brand */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"text-xl font-bold text-gray-900 tracking-wide\">\n              <div className=\"text-center\">\n                <div className=\"text-lg font-bold\">BRIGHT PATH</div>\n                <div className=\"text-sm font-medium\">EDUCATIONAL</div>\n                <div className=\"text-sm font-medium\">CONSULTING</div>\n              </div>\n            </Link>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <button\n              onClick={() => scrollToSection('about')}\n              className=\"text-gray-700 hover:text-gray-900 font-medium transition-colors duration-200\"\n            >\n              About\n            </button>\n            <button\n              onClick={() => scrollToSection('services')}\n              className=\"text-gray-700 hover:text-gray-900 font-medium transition-colors duration-200\"\n            >\n              Services\n            </button>\n            <button\n              onClick={() => scrollToSection('expertise')}\n              className=\"text-gray-700 hover:text-gray-900 font-medium transition-colors duration-200\"\n            >\n              Expertise\n            </button>\n            <button\n              onClick={() => scrollToSection('contact')}\n              className=\"text-gray-700 hover:text-gray-900 font-medium transition-colors duration-200\"\n            >\n              Contact\n            </button>\n          </nav>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              type=\"button\"\n              className=\"text-gray-700 hover:text-gray-900 focus:outline-none focus:text-gray-900\"\n              aria-label=\"Toggle menu\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,kBAAkB,CAAC;IACvB,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,QAAQ,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9C;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAoB;;;;;;kDACnC,8OAAC;wCAAI,WAAU;kDAAsB;;;;;;kDACrC,8OAAC;wCAAI,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;kCAM3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC9D,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/app/contact/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Header from '@/components/Header';\n\nexport default function Contact() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Handle form submission here\n    console.log('Form submitted:', formData);\n    // You can add form submission logic here\n  };\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Header />\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16\">\n          {/* Left Side - Contact Information */}\n          <div className=\"space-y-8\">\n            {/* Contact Heading */}\n            <div>\n              <div className=\"w-16 h-1 bg-black mb-6\"></div>\n              <h1 className=\"text-4xl font-bold text-black tracking-wide\">\n                CONTACT\n              </h1>\n            </div>\n\n            {/* Contact Details */}\n            <div className=\"space-y-6\">\n              <div>\n                <p className=\"text-gray-700 text-lg\">\n                  DMV area in Maryland\n                </p>\n              </div>\n\n              <div>\n                <p className=\"text-gray-700 text-lg\">\n                  Tel: (301)518-5305\n                </p>\n              </div>\n\n              <div>\n                <p className=\"text-gray-700 text-lg\">\n                  <EMAIL>\n                </p>\n              </div>\n            </div>\n\n            {/* Footer */}\n            <div className=\"pt-16\">\n              <p className=\"text-gray-500 text-sm\">\n                © 2023 by Bright Path Educational Consulting. Powered and secured by{' '}\n                <a\n                  href=\"https://www.wix.com\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"underline hover:text-gray-700 transition-colors\"\n                >\n                  Wix\n                </a>\n              </p>\n            </div>\n          </div>\n\n          {/* Right Side - Contact Form */}\n          <div className=\"space-y-6\">\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {/* Name Field */}\n              <div>\n                <label htmlFor=\"name\" className=\"block text-gray-700 text-sm mb-2\">\n                  Enter Your Name\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-3 border-2 border-gray-300 focus:border-gray-500 focus:outline-none transition-colors\"\n                  required\n                />\n              </div>\n\n              {/* Email Field */}\n              <div>\n                <label htmlFor=\"email\" className=\"block text-gray-700 text-sm mb-2\">\n                  Enter Your Email *\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border-2 border-gray-300 focus:border-gray-500 focus:outline-none transition-colors pr-12\"\n                    required\n                  />\n                  <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\n                    <div className=\"w-6 h-6 bg-red-500 rounded flex items-center justify-center\">\n                      <span className=\"text-white text-xs font-bold\">@</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Subject Field */}\n              <div>\n                <label htmlFor=\"subject\" className=\"block text-gray-700 text-sm mb-2\">\n                  Enter Your Subject\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"subject\"\n                  name=\"subject\"\n                  value={formData.subject}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-3 border-2 border-gray-300 focus:border-gray-500 focus:outline-none transition-colors\"\n                />\n              </div>\n\n              {/* Message Field */}\n              <div>\n                <label htmlFor=\"message\" className=\"block text-gray-700 text-sm mb-2\">\n                  Message\n                </label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  value={formData.message}\n                  onChange={handleChange}\n                  rows={6}\n                  className=\"w-full px-4 py-3 border-2 border-gray-300 focus:border-gray-500 focus:outline-none transition-colors resize-vertical\"\n                />\n              </div>\n\n              {/* Submit Button */}\n              <div className=\"flex justify-end\">\n                <button\n                  type=\"submit\"\n                  className=\"bg-black text-white px-8 py-3 hover:bg-gray-800 transition-colors duration-300 font-medium\"\n                >\n                  Submit\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,mBAAmB;IAC/B,yCAAyC;IAC3C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAG,WAAU;sDAA8C;;;;;;;;;;;;8CAM9D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDACC,cAAA,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;sDAKvC,8OAAC;sDACC,cAAA,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;sDAKvC,8OAAC;sDACC,cAAA,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;8CAOzC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;4CAAwB;4CACkC;0DACrE,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;sCAQP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDAEtC,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAAmC;;;;;;0DAGnE,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,IAAI;gDACpB,UAAU;gDACV,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAKZ,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAAmC;;;;;;0DAGpE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,WAAU;wDACV,QAAQ;;;;;;kEAEV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOvD,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAU,WAAU;0DAAmC;;;;;;0DAGtE,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,OAAO;gDACvB,UAAU;gDACV,WAAU;;;;;;;;;;;;kDAKd,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAU,WAAU;0DAAmC;;;;;;0DAGtE,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,OAAO;gDACvB,UAAU;gDACV,MAAM;gDACN,WAAU;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}
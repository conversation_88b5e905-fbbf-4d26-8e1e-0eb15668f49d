{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\n\nconst scrollToSection = (sectionId: string) => {\n  const element = document.getElementById(sectionId);\n  if (element) {\n    element.scrollIntoView({ behavior: 'smooth' });\n  }\n};\n\nexport default function Header() {\n  return (\n    <header className=\"bg-white shadow-sm fixed w-full top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-6\">\n          {/* Logo/Brand */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"text-xl font-bold text-gray-900 tracking-wide\">\n              <div className=\"text-center\">\n                <div className=\"text-lg font-bold\">BRIGHT PATH</div>\n                <div className=\"text-sm font-medium\">EDUCATIONAL</div>\n                <div className=\"text-sm font-medium\">CONSULTING</div>\n              </div>\n            </Link>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <button\n              onClick={() => scrollToSection('about')}\n              className=\"text-gray-700 hover:text-gray-900 font-medium transition-colors duration-200\"\n            >\n              About\n            </button>\n            <button\n              onClick={() => scrollToSection('services')}\n              className=\"text-gray-700 hover:text-gray-900 font-medium transition-colors duration-200\"\n            >\n              Services\n            </button>\n            <button\n              onClick={() => scrollToSection('expertise')}\n              className=\"text-gray-700 hover:text-gray-900 font-medium transition-colors duration-200\"\n            >\n              Expertise\n            </button>\n            <button\n              onClick={() => scrollToSection('contact')}\n              className=\"text-gray-700 hover:text-gray-900 font-medium transition-colors duration-200\"\n            >\n              Contact\n            </button>\n          </nav>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              type=\"button\"\n              className=\"text-gray-700 hover:text-gray-900 focus:outline-none focus:text-gray-900\"\n              aria-label=\"Toggle menu\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,kBAAkB,CAAC;IACvB,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,QAAQ,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9C;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAoB;;;;;;kDACnC,8OAAC;wCAAI,WAAU;kDAAsB;;;;;;kDACrC,8OAAC;wCAAI,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;kCAM3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC9D,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/ContactSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function ContactSection() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Handle form submission here\n    console.log('Form submitted:', formData);\n    // You can add form submission logic here\n  };\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16\">\n          {/* Left Side - Contact Information */}\n          <div className=\"space-y-8\">\n            {/* Contact Heading */}\n            <div>\n              <div className=\"w-16 h-1 bg-black mb-6\"></div>\n              <h2 className=\"text-4xl font-bold text-black tracking-wide mb-8\">\n                CONTACT\n              </h2>\n            </div>\n\n            {/* Contact Details */}\n            <div className=\"space-y-6\">\n              <div>\n                <p className=\"text-gray-700 text-lg\">\n                  DMV area in Maryland\n                </p>\n              </div>\n\n              <div>\n                <p className=\"text-gray-700 text-lg\">\n                  Tel: (301)518-5305\n                </p>\n              </div>\n\n              <div>\n                <p className=\"text-gray-700 text-lg\">\n                  <EMAIL>\n                </p>\n              </div>\n            </div>\n\n            {/* Footer */}\n            <div className=\"pt-16\">\n              <p className=\"text-gray-500 text-sm\">\n                © 2023 by Bright Path Educational Consulting. All rights reserved.\n              </p>\n            </div>\n          </div>\n\n          {/* Right Side - Contact Form */}\n          <div className=\"space-y-6\">\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {/* Name Field */}\n              <div>\n                <label htmlFor=\"name\" className=\"block text-gray-700 text-sm mb-2\">\n                  Enter Your Name\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-3 border-2 border-gray-300 focus:border-gray-500 focus:outline-none transition-colors\"\n                  required\n                />\n              </div>\n\n              {/* Email Field */}\n              <div>\n                <label htmlFor=\"email\" className=\"block text-gray-700 text-sm mb-2\">\n                  Enter Your Email *\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border-2 border-gray-300 focus:border-gray-500 focus:outline-none transition-colors pr-12\"\n                    required\n                  />\n                  <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\n                    <div className=\"w-6 h-6 bg-red-500 rounded flex items-center justify-center\">\n                      <span className=\"text-white text-xs font-bold\">@</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Subject Field */}\n              <div>\n                <label htmlFor=\"subject\" className=\"block text-gray-700 text-sm mb-2\">\n                  Enter Your Subject\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"subject\"\n                  name=\"subject\"\n                  value={formData.subject}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-3 border-2 border-gray-300 focus:border-gray-500 focus:outline-none transition-colors\"\n                />\n              </div>\n\n              {/* Message Field */}\n              <div>\n                <label htmlFor=\"message\" className=\"block text-gray-700 text-sm mb-2\">\n                  Message\n                </label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  value={formData.message}\n                  onChange={handleChange}\n                  rows={6}\n                  className=\"w-full px-4 py-3 border-2 border-gray-300 focus:border-gray-500 focus:outline-none transition-colors resize-vertical\"\n                />\n              </div>\n\n              {/* Submit Button */}\n              <div className=\"flex justify-end\">\n                <button\n                  type=\"submit\"\n                  className=\"bg-black text-white px-8 py-3 hover:bg-gray-800 transition-colors duration-300 font-medium\"\n                >\n                  Submit\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,mBAAmB;IAC/B,yCAAyC;IAC3C;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAG,WAAU;kDAAmD;;;;;;;;;;;;0CAMnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDACC,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;kDAKvC,8OAAC;kDACC,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;kDAKvC,8OAAC;kDACC,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAOzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAOzC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAAmC;;;;;;sDAGnE,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,IAAI;4CACpB,UAAU;4CACV,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAKZ,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAAmC;;;;;;sDAGpE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAU;oDACV,QAAQ;;;;;;8DAEV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOvD,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAU,WAAU;sDAAmC;;;;;;sDAGtE,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,OAAO;4CACvB,UAAU;4CACV,WAAU;;;;;;;;;;;;8CAKd,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAU,WAAU;sDAAmC;;;;;;sDAGtE,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,OAAO;4CACvB,UAAU;4CACV,MAAM;4CACN,WAAU;;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/ChatWidget.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function ChatWidget() {\n  const [isOpen, setIsOpen] = useState(false);\n\n  return (\n    <div className=\"fixed bottom-6 right-6 z-50\">\n      {/* Chat <PERSON> */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"bg-blue-600 hover:bg-blue-700 text-white rounded-full p-4 shadow-lg transition-all duration-300 flex items-center justify-center\"\n        aria-label=\"Open chat\"\n      >\n        {isOpen ? (\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n          </svg>\n        ) : (\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n          </svg>\n        )}\n      </button>\n\n      {/* Chat Window */}\n      {isOpen && (\n        <div className=\"absolute bottom-16 right-0 w-80 h-96 bg-white rounded-lg shadow-xl border border-gray-200 flex flex-col\">\n          {/* Header */}\n          <div className=\"bg-blue-600 text-white p-4 rounded-t-lg\">\n            <h3 className=\"font-semibold\">Let's Chat!</h3>\n            <p className=\"text-sm opacity-90\">We're here to help you succeed</p>\n          </div>\n\n          {/* Messages Area */}\n          <div className=\"flex-1 p-4 overflow-y-auto\">\n            <div className=\"space-y-3\">\n              <div className=\"bg-gray-100 rounded-lg p-3 max-w-xs\">\n                <p className=\"text-sm\">Hello! How can we help you with your educational goals today?</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Input Area */}\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"flex space-x-2\">\n              <input\n                type=\"text\"\n                placeholder=\"Type your message...\"\n                className=\"flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n              <button className=\"bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-4 py-2 text-sm font-medium transition-colors\">\n                Send\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,cAAW;0BAEV,uBACC,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;yCAGvE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;YAM1E,wBACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAgB;;;;;;0CAC9B,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;kCAM7B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCAAO,WAAU;8CAAsG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStI", "debugId": null}}]}
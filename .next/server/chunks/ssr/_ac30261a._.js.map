{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/app/about/page.tsx"], "sourcesContent": ["import Header from '@/components/Header';\nimport Image from 'next/image';\n\nexport default function About() {\n  return (\n    <div className=\"min-h-screen\">\n      <Header />\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        {/* Section Header */}\n        <div className=\"mb-16\">\n          <div className=\"flex items-center mb-8\">\n            <div className=\"w-16 h-0.5 bg-gray-400 mr-6\"></div>\n            <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 tracking-wide\">ABOUT US</h1>\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-start\">\n          {/* Left Column - Text Content */}\n          <div className=\"space-y-6\">\n            <p className=\"text-gray-700 leading-relaxed\">\n              At Bright Path Educational Consulting, we believe that every child has the potential to succeed when provided with the right guidance and support. Our mission is to ensure that all children have access to the resources they need to thrive in every area of academia. We work closely with parents to tailor our services to the unique needs of each child, offering tutoring, admissions counseling, and special education advocacy to help them achieve their academic goals.\n            </p>\n\n            <p className=\"text-gray-700 leading-relaxed\">\n              Founded in 2021, Bright Path was born out of a desire to extend our role beyond the classroom. We engage with children in more comfortable, personalized settings, providing them with the tools and confidence they need to excel. Our dedicated team of professional educators brings over six decades of experience in delivering exemplary educational services to students. Under the leadership of our founder, a former director of special education services, we are committed to making a lasting impact on the lives of the children we serve.\n            </p>\n\n            <p className=\"text-gray-700 leading-relaxed\">\n              Based in the DMV area, we are proud to support families in our community, guiding each child along their unique educational journey.\n            </p>\n          </div>\n\n          {/* Right Column - Image */}\n          <div className=\"relative\">\n            <Image\n              src=\"/about-us-image.jpg\"\n              alt=\"Educational consulting session with students and instructor\"\n              width={600}\n              height={400}\n              className=\"rounded-lg shadow-lg object-cover w-full h-auto\"\n              priority\n            />\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAG,WAAU;8CAA6D;;;;;;;;;;;;;;;;;kCAK/E,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAI7C,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAI7C,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAM/C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;oCACV,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB", "debugId": null}}]}
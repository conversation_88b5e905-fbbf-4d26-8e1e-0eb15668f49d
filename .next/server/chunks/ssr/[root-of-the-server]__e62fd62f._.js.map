{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/brightpath/src/components/ChatWidget.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function ChatWidget() {\n  const [isOpen, setIsOpen] = useState(false);\n\n  return (\n    <div className=\"fixed bottom-6 right-6 z-50\">\n      {/* Chat <PERSON> */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"bg-blue-600 hover:bg-blue-700 text-white rounded-full p-4 shadow-lg transition-all duration-300 flex items-center justify-center\"\n        aria-label=\"Open chat\"\n      >\n        {isOpen ? (\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n          </svg>\n        ) : (\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n          </svg>\n        )}\n      </button>\n\n      {/* Chat Window */}\n      {isOpen && (\n        <div className=\"absolute bottom-16 right-0 w-80 h-96 bg-white rounded-lg shadow-xl border border-gray-200 flex flex-col\">\n          {/* Header */}\n          <div className=\"bg-blue-600 text-white p-4 rounded-t-lg\">\n            <h3 className=\"font-semibold\">Let's Chat!</h3>\n            <p className=\"text-sm opacity-90\">We're here to help you succeed</p>\n          </div>\n\n          {/* Messages Area */}\n          <div className=\"flex-1 p-4 overflow-y-auto\">\n            <div className=\"space-y-3\">\n              <div className=\"bg-gray-100 rounded-lg p-3 max-w-xs\">\n                <p className=\"text-sm\">Hello! How can we help you with your educational goals today?</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Input Area */}\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"flex space-x-2\">\n              <input\n                type=\"text\"\n                placeholder=\"Type your message...\"\n                className=\"flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n              <button className=\"bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-4 py-2 text-sm font-medium transition-colors\">\n                Send\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,cAAW;0BAEV,uBACC,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;yCAGvE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;YAM1E,wBACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAgB;;;;;;0CAC9B,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;kCAM7B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCAAO,WAAU;8CAAsG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStI", "debugId": null}}]}